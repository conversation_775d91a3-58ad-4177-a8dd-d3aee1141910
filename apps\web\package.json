{"name": "web", "version": "0.1.2", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "analyze": "cross-env ANALYZE=true next build", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@config/tailwind": "workspace:^", "@geon-map/core": "workspace:^", "@geon-map/react-odf": "workspace:^", "@geon-map/react-ui": "workspace:^", "@geon-query/model": "workspace:^", "@geon-query/react-query": "workspace:*", "@geon-ui/react": "workspace:^", "@svgr/webpack": "^8.1.0", "lucide-react": "^0.534.0", "next": "^15.4.5", "react": "^19.1.1", "react-dom": "^19.1.1", "zustand": "^5.0.6"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@next/bundle-analyzer": "^15.4.5", "@storybook/addon-docs": "9.0.18", "@storybook/addon-links": "^9.0.18", "@storybook/nextjs": "9.0.18", "@tailwindcss/postcss": "^4.1.11", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.17.0", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "eslint": "^9.32.0", "eslint-plugin-storybook": "9.0.18", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss-loader": "^8.1.1", "prettier-plugin-tailwindcss": "^0.6.14", "raw-loader": "^4.0.2", "storybook": "9.0.18", "ts-node": "^10.9.2", "typescript": "5.8.3"}}